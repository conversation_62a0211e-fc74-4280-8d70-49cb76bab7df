import React, { useState, useEffect } from 'react';
import LocationPanel from './components/LocationPanel';
import DrawingPad from './components/DrawingPad';
import NetworkMonitor from './components/NetworkMonitor';
import SharePanel from './components/SharePanel';

interface EmergencyData {
  location: {
    latitude: number | null;
    longitude: number | null;
    accuracy: number | null;
    timestamp: number | null;
  };
  drawing: string | null;
  networkInfo: {
    effectiveType: string;
    downlink: number;
    rtt: number;
    saveData: boolean;
  } | null;
  notes: string;
}

function App() {
  const [emergencyData, setEmergencyData] = useState<EmergencyData>({
    location: {
      latitude: null,
      longitude: null,
      accuracy: null,
      timestamp: null,
    },
    drawing: null,
    networkInfo: null,
    notes: '',
  });

  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Auto-save to localStorage
  useEffect(() => {
    localStorage.setItem('emergencyData', JSON.stringify(emergencyData));
  }, [emergencyData]);

  // Load from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('emergencyData');
    if (saved) {
      try {
        setEmergencyData(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load saved data:', error);
      }
    }
  }, []);

  const updateLocation = (location: EmergencyData['location']) => {
    setEmergencyData(prev => ({ ...prev, location }));
  };

  const updateDrawing = (drawing: string) => {
    setEmergencyData(prev => ({ ...prev, drawing }));
  };

  const updateNetworkInfo = (networkInfo: EmergencyData['networkInfo']) => {
    setEmergencyData(prev => ({ ...prev, networkInfo }));
  };

  const updateNotes = (notes: string) => {
    setEmergencyData(prev => ({ ...prev, notes }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-red-600 mb-2">
            🆘 Smart Emergency Assistant
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Share your location, draw surroundings, and monitor connectivity in emergencies
          </p>
          <div className="flex items-center justify-center mt-4">
            <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              isOnline 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              <div className={`w-2 h-2 rounded-full mr-2 ${
                isOnline ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              {isOnline ? 'Online' : 'Offline'}
            </div>
          </div>
        </div>

        {/* Main Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Location Panel */}
          <LocationPanel
            location={emergencyData.location}
            onLocationUpdate={updateLocation}
          />

          {/* Network Monitor */}
          <NetworkMonitor
            networkInfo={emergencyData.networkInfo}
            onNetworkUpdate={updateNetworkInfo}
            isOnline={isOnline}
          />
        </div>

        {/* Drawing Pad */}
        <div className="mb-6">
          <DrawingPad
            drawing={emergencyData.drawing}
            onDrawingUpdate={updateDrawing}
          />
        </div>

        {/* Notes Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">
            📝 Additional Notes
          </h3>
          <textarea
            value={emergencyData.notes}
            onChange={(e) => updateNotes(e.target.value)}
            placeholder="Describe your situation, nearby landmarks, or any other important details..."
            className="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
          />
        </div>

        {/* Share Panel */}
        <SharePanel emergencyData={emergencyData} />

        {/* Footer */}
        <div className="text-center mt-8 text-gray-500 text-sm">
          <p>🔒 All data is stored locally on your device for privacy</p>
          <p className="mt-1">Built with React + Tailwind CSS + Modern Web APIs</p>
        </div>
      </div>
    </div>
  );
}

export default App;
