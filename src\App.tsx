import React, { useState, useEffect } from 'react';

interface EmergencyData {
  location: {
    latitude: number | null;
    longitude: number | null;
    accuracy: number | null;
    timestamp: number | null;
  };
  drawing: string | null;
  networkInfo: {
    effectiveType: string;
    downlink: number;
    rtt: number;
    saveData: boolean;
  } | null;
  notes: string;
}

function App() {
  const [emergencyData, setEmergencyData] = useState<EmergencyData>({
    location: {
      latitude: null,
      longitude: null,
      accuracy: null,
      timestamp: null,
    },
    drawing: null,
    networkInfo: null,
    notes: '',
  });

  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Auto-save to localStorage
  useEffect(() => {
    localStorage.setItem('emergencyData', JSON.stringify(emergencyData));
  }, [emergencyData]);

  // Load from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('emergencyData');
    if (saved) {
      try {
        setEmergencyData(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load saved data:', error);
      }
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-red-600 mb-2">
            🆘 Smart Emergency Assistant
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Share your location, draw surroundings, and monitor connectivity in emergencies
          </p>
          <div className="flex items-center justify-center mt-4">
            <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              isOnline 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              <div className={`w-2 h-2 rounded-full mr-2 ${
                isOnline ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              {isOnline ? 'Online' : 'Offline'}
            </div>
          </div>
        </div>

        {/* Placeholder for components - will be added step by step */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            🚧 Application Loading...
          </h2>
          <p className="text-gray-600">
            The Smart Emergency Assistant is being set up with all the requested features:
          </p>
          <ul className="mt-4 space-y-2 text-gray-600">
            <li>📍 Location Panel (Geolocation API)</li>
            <li>🎨 Drawing Pad (Canvas API)</li>
            <li>📶 Network Monitor (Network Information API)</li>
            <li>📤 Share Panel (Web Share API)</li>
            <li>💾 Auto-save & Offline Support</li>
          </ul>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-gray-500 text-sm">
          <p>🔒 All data is stored locally on your device for privacy</p>
          <p className="mt-1">Built with React + Tailwind CSS + Modern Web APIs</p>
        </div>
      </div>
    </div>
  );
}

export default App;
