import React, { useEffect, useState } from 'react';

interface NetworkInfo {
  effectiveType: string;
  downlink: number;
  rtt: number;
  saveData: boolean;
}

interface NetworkMonitorProps {
  networkInfo: NetworkInfo | null;
  onNetworkUpdate: (networkInfo: NetworkInfo | null) => void;
  isOnline: boolean;
}

// Extend Navigator interface for TypeScript
declare global {
  interface Navigator {
    connection?: {
      effectiveType: string;
      downlink: number;
      rtt: number;
      saveData: boolean;
      addEventListener: (type: string, listener: EventListener) => void;
      removeEventListener: (type: string, listener: EventListener) => void;
    };
  }
}

const NetworkMonitor: React.FC<NetworkMonitorProps> = ({ 
  networkInfo, 
  onNetworkUpdate, 
  isOnline 
}) => {
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    // Check if Network Information API is supported
    if ('connection' in navigator) {
      setIsSupported(true);
      updateNetworkInfo();

      const connection = navigator.connection!;
      const handleChange = () => updateNetworkInfo();

      connection.addEventListener('change', handleChange);

      return () => {
        connection.removeEventListener('change', handleChange);
      };
    } else {
      setIsSupported(false);
      onNetworkUpdate(null);
    }
  }, []);

  const updateNetworkInfo = () => {
    if (navigator.connection) {
      const connection = navigator.connection;
      onNetworkUpdate({
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      });
    }
  };

  const getConnectionQuality = (effectiveType: string, downlink: number) => {
    if (!isOnline) return { quality: 'offline', color: 'red', description: 'No Connection' };
    
    if (effectiveType === '4g' && downlink > 1.5) {
      return { quality: 'excellent', color: 'green', description: 'Excellent' };
    } else if (effectiveType === '4g' || (effectiveType === '3g' && downlink > 0.5)) {
      return { quality: 'good', color: 'blue', description: 'Good' };
    } else if (effectiveType === '3g' || effectiveType === '2g') {
      return { quality: 'fair', color: 'yellow', description: 'Fair' };
    } else {
      return { quality: 'poor', color: 'orange', description: 'Poor' };
    }
  };

  const getSignalBars = (quality: string) => {
    const bars = [];
    const levels = {
      'offline': 0,
      'poor': 1,
      'fair': 2,
      'good': 3,
      'excellent': 4
    };
    
    const level = levels[quality as keyof typeof levels] || 0;
    
    for (let i = 0; i < 4; i++) {
      bars.push(
        <div
          key={i}
          className={`w-1 bg-gray-300 rounded-sm ${
            i < level ? 'bg-current' : ''
          }`}
          style={{ height: `${(i + 1) * 4 + 4}px` }}
        />
      );
    }
    
    return bars;
  };

  const runSpeedTest = async () => {
    if (!isOnline) return;
    
    try {
      const startTime = performance.now();
      const response = await fetch('https://httpbin.org/bytes/1024', { 
        cache: 'no-cache' 
      });
      const endTime = performance.now();
      
      if (response.ok) {
        const duration = endTime - startTime;
        const speed = (1024 * 8) / (duration / 1000); // bits per second
        const speedKbps = speed / 1000; // kilobits per second
        
        alert(`Speed Test Result: ${speedKbps.toFixed(2)} Kbps`);
      }
    } catch (error) {
      alert('Speed test failed. Check your connection.');
    }
  };

  if (!isSupported) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">
          📶 Network Monitor
        </h3>
        <div className="text-center py-8">
          <div className="text-gray-400 text-4xl mb-4">📱</div>
          <p className="text-gray-600">
            Network Information API not supported in this browser
          </p>
          <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mt-4 ${
            isOnline 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            <div className={`w-2 h-2 rounded-full mr-2 ${
              isOnline ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            {isOnline ? 'Online' : 'Offline'}
          </div>
        </div>
      </div>
    );
  }

  const quality = networkInfo ? getConnectionQuality(networkInfo.effectiveType, networkInfo.downlink) : 
    { quality: 'offline', color: 'red', description: 'No Connection' };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-semibold text-gray-800">
          📶 Network Monitor
        </h3>
        <button
          onClick={runSpeedTest}
          disabled={!isOnline}
          className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
            isOnline
              ? 'bg-blue-500 text-white hover:bg-blue-600'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          🚀 Speed Test
        </button>
      </div>

      {/* Signal Strength Indicator */}
      <div className="flex items-center justify-center mb-6">
        <div className={`flex items-end gap-1 text-${quality.color}-500`}>
          {getSignalBars(quality.quality)}
        </div>
        <div className="ml-4 text-center">
          <div className={`text-lg font-semibold text-${quality.color}-600`}>
            {quality.description}
          </div>
          <div className="text-sm text-gray-500">
            {networkInfo?.effectiveType?.toUpperCase() || 'N/A'}
          </div>
        </div>
      </div>

      {/* Network Details */}
      {networkInfo && (
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Connection Type:</span>
            <span className="font-mono text-sm">
              {networkInfo.effectiveType.toUpperCase()}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-600">Download Speed:</span>
            <span className="font-mono text-sm">
              {networkInfo.downlink} Mbps
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-600">Round Trip Time:</span>
            <span className="font-mono text-sm">
              {networkInfo.rtt} ms
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-600">Data Saver:</span>
            <span className={`text-sm font-medium ${
              networkInfo.saveData ? 'text-orange-600' : 'text-green-600'
            }`}>
              {networkInfo.saveData ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>
      )}

      {/* Connection Status */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Status:</span>
          <div className={`flex items-center text-sm font-medium ${
            isOnline ? 'text-green-600' : 'text-red-600'
          }`}>
            <div className={`w-2 h-2 rounded-full mr-2 ${
              isOnline ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            {isOnline ? 'Connected' : 'Disconnected'}
          </div>
        </div>
      </div>

      <div className="mt-4 text-xs text-gray-500">
        <p>💡 <strong>Tip:</strong> Poor connection? Try moving to a different location or higher ground</p>
      </div>
    </div>
  );
};

export default NetworkMonitor;
