import React, { useState } from 'react';

interface EmergencyData {
  location: {
    latitude: number | null;
    longitude: number | null;
    accuracy: number | null;
    timestamp: number | null;
  };
  drawing: string | null;
  networkInfo: {
    effectiveType: string;
    downlink: number;
    rtt: number;
    saveData: boolean;
  } | null;
  notes: string;
}

interface SharePanelProps {
  emergencyData: EmergencyData;
}

const SharePanel: React.FC<SharePanelProps> = ({ emergencyData }) => {
  const [isSharing, setIsSharing] = useState(false);

  const generateEmergencyMessage = () => {
    const { location, notes, networkInfo } = emergencyData;
    
    let message = "🆘 EMERGENCY ALERT 🆘\n\n";
    
    if (location.latitude && location.longitude) {
      message += `📍 LOCATION:\n`;
      message += `Coordinates: ${location.latitude}, ${location.longitude}\n`;
      message += `Google Maps: https://www.google.com/maps?q=${location.latitude},${location.longitude}\n`;
      if (location.accuracy) {
        message += `Accuracy: ±${location.accuracy.toFixed(0)}m\n`;
      }
      if (location.timestamp) {
        message += `Time: ${new Date(location.timestamp).toLocaleString()}\n`;
      }
      message += "\n";
    }

    if (notes.trim()) {
      message += `📝 SITUATION:\n${notes.trim()}\n\n`;
    }

    if (networkInfo) {
      message += `📶 NETWORK STATUS:\n`;
      message += `Connection: ${networkInfo.effectiveType.toUpperCase()}\n`;
      message += `Speed: ${networkInfo.downlink} Mbps\n`;
      message += `Latency: ${networkInfo.rtt}ms\n\n`;
    }

    message += `⏰ Generated: ${new Date().toLocaleString()}\n`;
    message += `🔗 Sent via Smart Emergency Assistant`;

    return message;
  };

  const shareViaWebShare = async () => {
    if (!navigator.share) {
      alert('Web Share API not supported in this browser');
      return;
    }

    setIsSharing(true);
    try {
      const message = generateEmergencyMessage();
      
      const shareData: ShareData = {
        title: '🆘 Emergency Alert',
        text: message,
      };

      // Add drawing if available
      if (emergencyData.drawing) {
        try {
          const response = await fetch(emergencyData.drawing);
          const blob = await response.blob();
          const file = new File([blob], 'emergency-sketch.png', { type: 'image/png' });
          shareData.files = [file];
        } catch (error) {
          console.error('Failed to include drawing in share:', error);
        }
      }

      await navigator.share(shareData);
    } catch (error) {
      if ((error as Error).name !== 'AbortError') {
        console.error('Error sharing:', error);
        alert('Failed to share. Please try copying the message instead.');
      }
    } finally {
      setIsSharing(false);
    }
  };

  const copyToClipboard = async () => {
    const message = generateEmergencyMessage();
    
    try {
      await navigator.clipboard.writeText(message);
      alert('Emergency message copied to clipboard!');
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = message;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Emergency message copied to clipboard!');
    }
  };

  const shareViaWhatsApp = () => {
    const message = encodeURIComponent(generateEmergencyMessage());
    const url = `https://wa.me/?text=${message}`;
    window.open(url, '_blank');
  };

  const shareViaEmail = () => {
    const subject = encodeURIComponent('🆘 Emergency Alert');
    const body = encodeURIComponent(generateEmergencyMessage());
    const url = `mailto:?subject=${subject}&body=${body}`;
    window.open(url);
  };

  const shareViaSMS = () => {
    const message = encodeURIComponent(generateEmergencyMessage());
    const url = `sms:?body=${message}`;
    window.open(url);
  };

  const downloadEmergencyData = () => {
    const data = {
      ...emergencyData,
      generatedAt: new Date().toISOString(),
      message: generateEmergencyMessage()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { 
      type: 'application/json' 
    });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `emergency-data-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const hasData = emergencyData.location.latitude || emergencyData.notes.trim() || emergencyData.drawing;

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold text-gray-800 mb-4">
        📤 Share Emergency Info
      </h3>

      {!hasData && (
        <div className="text-center py-8 text-gray-500">
          <div className="text-4xl mb-4">📋</div>
          <p>Add location, notes, or drawing to enable sharing</p>
        </div>
      )}

      {hasData && (
        <>
          {/* Preview */}
          <div className="bg-gray-50 rounded-lg p-4 mb-4 max-h-40 overflow-y-auto">
            <h4 className="font-medium text-gray-700 mb-2">Message Preview:</h4>
            <pre className="text-xs text-gray-600 whitespace-pre-wrap">
              {generateEmergencyMessage()}
            </pre>
          </div>

          {/* Share Options */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
            {navigator.share && (
              <button
                onClick={shareViaWebShare}
                disabled={isSharing}
                className="flex flex-col items-center p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
              >
                <span className="text-xl mb-1">📱</span>
                <span className="text-xs">Share</span>
              </button>
            )}

            <button
              onClick={copyToClipboard}
              className="flex flex-col items-center p-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              <span className="text-xl mb-1">📋</span>
              <span className="text-xs">Copy</span>
            </button>

            <button
              onClick={shareViaWhatsApp}
              className="flex flex-col items-center p-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              <span className="text-xl mb-1">💬</span>
              <span className="text-xs">WhatsApp</span>
            </button>

            <button
              onClick={shareViaEmail}
              className="flex flex-col items-center p-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              <span className="text-xl mb-1">📧</span>
              <span className="text-xs">Email</span>
            </button>

            <button
              onClick={shareViaSMS}
              className="flex flex-col items-center p-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
            >
              <span className="text-xl mb-1">💬</span>
              <span className="text-xs">SMS</span>
            </button>

            <button
              onClick={downloadEmergencyData}
              className="flex flex-col items-center p-3 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors"
            >
              <span className="text-xl mb-1">💾</span>
              <span className="text-xs">Download</span>
            </button>
          </div>

          {/* Emergency Contacts */}
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-700 mb-3">🚨 Quick Emergency Contacts</h4>
            <div className="grid grid-cols-3 gap-2 text-center">
              <a
                href="tel:911"
                className="p-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
              >
                <div className="font-bold">911</div>
                <div className="text-xs">Emergency</div>
              </a>
              <a
                href="tel:311"
                className="p-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
              >
                <div className="font-bold">311</div>
                <div className="text-xs">Non-Emergency</div>
              </a>
              <a
                href="tel:211"
                className="p-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
              >
                <div className="font-bold">211</div>
                <div className="text-xs">Help Line</div>
              </a>
            </div>
          </div>
        </>
      )}

      <div className="mt-4 text-xs text-gray-500">
        <p>💡 <strong>Tip:</strong> Share your location with trusted contacts before venturing into unfamiliar areas</p>
      </div>
    </div>
  );
};

export default SharePanel;
