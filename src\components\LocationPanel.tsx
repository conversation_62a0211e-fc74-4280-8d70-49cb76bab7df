import React, { useState } from 'react';

interface LocationData {
  latitude: number | null;
  longitude: number | null;
  accuracy: number | null;
  timestamp: number | null;
}

interface LocationPanelProps {
  location: LocationData;
  onLocationUpdate: (location: LocationData) => void;
}

const LocationPanel: React.FC<LocationPanelProps> = ({ location, onLocationUpdate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by this browser');
      return;
    }

    setIsLoading(true);
    setError(null);

    const options: PositionOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000
    };

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        onLocationUpdate({
          latitude,
          longitude,
          accuracy,
          timestamp: Date.now()
        });
        setIsLoading(false);
      },
      (error) => {
        let errorMessage = 'Failed to get location';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out';
            break;
        }
        setError(errorMessage);
        setIsLoading(false);
      },
      options
    );
  };

  const formatCoordinate = (coord: number | null, type: 'lat' | 'lng') => {
    if (coord === null) return 'N/A';
    const direction = type === 'lat' ? (coord >= 0 ? 'N' : 'S') : (coord >= 0 ? 'E' : 'W');
    return `${Math.abs(coord).toFixed(6)}° ${direction}`;
  };

  const formatTimestamp = (timestamp: number | null) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleString();
  };

  const copyToClipboard = () => {
    if (location.latitude && location.longitude) {
      const locationText = `Location: ${location.latitude}, ${location.longitude}`;
      navigator.clipboard.writeText(locationText).then(() => {
        // Could add a toast notification here
      });
    }
  };

  const openInMaps = () => {
    if (location.latitude && location.longitude) {
      const url = `https://www.google.com/maps?q=${location.latitude},${location.longitude}`;
      window.open(url, '_blank');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-semibold text-gray-800">
          📍 Location Panel
        </h3>
        <button
          onClick={getCurrentLocation}
          disabled={isLoading}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            isLoading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-red-500 text-white hover:bg-red-600'
          }`}
        >
          {isLoading ? 'Getting Location...' : 'Get Location'}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}

      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Latitude:</span>
          <span className="font-mono text-sm">
            {formatCoordinate(location.latitude, 'lat')}
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-600">Longitude:</span>
          <span className="font-mono text-sm">
            {formatCoordinate(location.longitude, 'lng')}
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-600">Accuracy:</span>
          <span className="font-mono text-sm">
            {location.accuracy ? `±${location.accuracy.toFixed(0)}m` : 'N/A'}
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-600">Last Updated:</span>
          <span className="font-mono text-xs">
            {formatTimestamp(location.timestamp)}
          </span>
        </div>
      </div>

      {location.latitude && location.longitude && (
        <div className="flex gap-2 mt-4">
          <button
            onClick={copyToClipboard}
            className="flex-1 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
          >
            📋 Copy Coordinates
          </button>
          <button
            onClick={openInMaps}
            className="flex-1 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
          >
            🗺️ Open in Maps
          </button>
        </div>
      )}

      <div className="mt-4 text-xs text-gray-500">
        <p>💡 <strong>Tip:</strong> Enable location services for better accuracy</p>
      </div>
    </div>
  );
};

export default LocationPanel;
